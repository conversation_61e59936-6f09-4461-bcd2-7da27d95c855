import { useEffect, useRef } from "react";

interface TimerControlsProp {
    handleStart: () => void;
    handleReset: () => void;
    isRunning: boolean;
}

const TimerControls = ({
    handleStart,
    handleReset,
    isRunning,
}: TimerControlsProp) => {
    const startRef = useRef<HTMLButtonElement>(null);

    useEffect(() => {
        startRef.current?.focus();
    }, []);

    return (
        <div className="flex justify-center gap-2">
            <button
                ref={startRef}
                onClick={handleStart}
                className="border px-4 py-2 bg-green-200 focus:bg-green-400 focus:outline-none hover:bg-green-300 cursor-pointer "
            >
                {isRunning ? "Pause" : "Start"}
            </button>
            <button
                onClick={handleReset}
                className="border px-4 py-2 bg-amber-200 focus:bg-amber-300 focus:outline-none hover:bg-amber-300 cursor-pointer"
            >
                Reset
            </button>
        </div>
    );
};

export default TimerControls;
