import { Timer as TimerIcon } from "lucide-react";
import { type Ref } from "react";
import { type Time } from "../types/types";

interface TimerDisplayProp {
    time: Time;
    timerRef: Ref<HTMLHeadingElement>;
}

const TimerDisplay = ({ timerRef, time }: TimerDisplayProp) => {
    return (
        <div className="flex mx-auto m-4 justify-center gap-2 max-w-[250px] border p-4  items-center ">
            <span>
                <TimerIcon />
            </span>
            <h1 className="text-4xl font-bold" ref={timerRef}>
                {time.h <= 9 ? "0" + time.h : time.h}:
                {time.m <= 9 ? "0" + time.m : time.m}:
                {time.s <= 9 ? "0" + time.s : time.s}
            </h1>
        </div>
    );
};

export default TimerDisplay;
