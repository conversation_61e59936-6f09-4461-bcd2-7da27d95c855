import { useEffect, useRef, useState } from "react";
import TimerDisplay from "./TimerDisplay";
import TimerControls from "./TimerControls";
import { type Time } from "../types/types";

const Timer = () => {
    const timerRef = useRef<HTMLHeadingElement>(null);
    const intervalId = useRef<number | undefined>(undefined);
    const [time, setTime] = useState<Time>(
        JSON.parse(localStorage.getItem("time") || '{"h":0,"m":0,"s":0}')
    );
    const [isRunning, setIsRunning] = useState(false);

    const handleStart = () => {
        if (!isRunning) {
            timerRef.current?.classList.add("animate-pulse");
            intervalId.current = setInterval(() => {
                setTime((prevTime) => {
                    const { h, m, s } = prevTime;
                    if (s === 59) {
                        if (m === 59) {
                            return { h: h + 1, m: 0, s: 0 };
                        }
                        return { h, m: m + 1, s: 0 };
                    }
                    return { h, m, s: s + 1 };
                });
            }, 1000);
        } else {
            timerRef.current?.classList.remove("animate-pulse");
            clearInterval(intervalId.current);
            localStorage.setItem("time", JSON.stringify(time));
        }
        setIsRunning(!isRunning);
    };

    const handleReset = () => {
        clearInterval(intervalId.current);
        timerRef.current?.classList.remove("animate-pulse");
        setIsRunning(false);
        setTime({ h: 0, m: 0, s: 0 });
        localStorage.setItem("time", JSON.stringify({ h: 0, m: 0, s: 0 }));
    };

    useEffect(() => {
        localStorage.setItem("time", JSON.stringify(time));
    }, [time]);

    return (
        <div className="border bg-amber-100 p-10 m-10 max-w-[450px] mx-auto ">
            <TimerDisplay time={time} timerRef={timerRef} />
            <TimerControls
                handleStart={handleStart}
                handleReset={handleReset}
                isRunning={isRunning}
            />
        </div>
    );
};

export default Timer;
