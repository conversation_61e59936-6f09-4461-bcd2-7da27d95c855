import { useEffect, useRef } from "react";

const RefExample = () => {
    const inputRef = useRef<HTMLInputElement>(null);

    const handleSubmit = () => {
        console.log(inputRef.current);
    };

    useEffect(() => {
        inputRef.current?.focus();
    }, []);

    return (
        <div className="max-w-md mx-auto mt-10 p-6 bg-gray-100 rounded-lg shadow-lg text-center">
            <h2 className="text-2xl font-bold mb-4">useRef Example</h2>
            <input
                type="text"
                className="w-full p-2 border rounded-lg border-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
                id=""
                placeholder="Type something ..."
                ref={inputRef}
            />
            <button
                onClick={handleSubmit}
                className="mt-2 bg-blue-200 text-white px-3 py-2 rounded hover:bg-blue-600 cursor-pointer"
            >
                Submit
            </button>
        </div>
    );
};

export default RefExample;
