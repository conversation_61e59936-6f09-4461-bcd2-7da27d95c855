{"name": "crypto-proxy", "version": "1.0.0", "description": "Backend proxy for crypto API", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js"}, "dependencies": {"axios": "^1.10.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.1", "memory-cache": "^0.2.0", "express-validator": "^7.0.1", "compression": "^1.7.4", "helmet": "^7.1.0"}, "devDependencies": {"nodemon": "^3.0.2"}}