require("dotenv").config();
const express = require("express");
const cors = require("cors");
const axios = require("axios");
const rateLimit = require("express-rate-limit");
const cache = require("memory-cache");
const { check, validationResult } = require("express-validator");
const compression = require("compression");
const helmet = require("helmet");

const app = express();
const port = process.env.PORT || 4000;

// Security middleware
app.use(helmet());
app.use(compression());

// Rate limiting middleware
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000,
    max: 100,
    standardHeaders: true,
    legacyHeaders: false,
});

app.use(cors());
app.use(limiter);

// Cache middleware
const cacheMiddleware = (duration) => {
    return (req, res, next) => {
        const key = "__express__" + req.originalUrl || req.url;
        const cachedBody = cache.get(key);
        if (cachedBody) {
            res.send(cachedBody);
            return;
        } else {
            res.sendResponse = res.send;
            res.send = (body) => {
                cache.put(key, body, duration * 1000);
                res.sendResponse(body);
            };
            next();
        }
    };
};

// Request validation middleware
const validateRequest = [
    check("vs_currency").optional().isIn(["usd", "eur", "btc", "eth"]),
    check("days").optional().isInt({ min: 1, max: 365 }),
    check("page").optional().isInt({ min: 1 }),
    check("per_page").optional().isInt({ min: 1, max: 100 }),
];

// Proxy endpoint for Coingecko API
app.get("/api/coingecko/:endpoint", validateRequest, async (req, res) => {
    try {
        // Validate request
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }

        // Use cache for 15 minutes
        app.use(cacheMiddleware(15 * 60));

        // First check if we have the API key
        if (!process.env.VITE_API_KEY) {
            return res.status(500).json({
                error: "API key not configured",
            });
        }

        const response = await axios.get(
            `https://api.coingecko.com/api/v3/${req.params.endpoint}`,
            {
                params: req.query,
                headers: {
                    "x-access-token": process.env.VITE_API_KEY,
                    Accept: "application/json",
                },
            }
        );

        // Add rate limit headers
        res.setHeader("X-RateLimit-Limit", "100");
        res.setHeader("X-RateLimit-Remaining", "100");
        res.setHeader("X-RateLimit-Reset", Date.now() + 15 * 60 * 1000);

        res.json(response.data);
    } catch (error) {
        console.error(error);

        // Handle different error types
        if (axios.isAxiosError(error)) {
            if (error.response) {
                // The request was made and the server responded with a status code
                return res.status(error.response.status).json({
                    error: error.response.data.message || "API Error",
                });
            } else if (error.request) {
                // The request was made but no response was received
                return res.status(504).json({
                    error: "Gateway Timeout",
                });
            }
        }

        res.status(500).json({
            error: "Internal Server Error",
        });
    }
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({
        error: "Internal Server Error",
    });
});

app.listen(port, () => {
    console.log(`Server running on port ${port}`);
    console.log("Security features enabled:");
    console.log("- Rate limiting (100 requests per 15 minutes)");
    console.log("- Response caching (15 minutes)");
    console.log("- Request validation");
    console.log("- Security headers via Helmet");
    console.log("- Compression enabled");
});
